<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <title>Excalidraw</title>
    <!-- Load Excalidraw CSS -->
    <link rel="stylesheet" href="./assets/index-Bnrc8hOs.css">
    <style>
        html, body, #root {
            height: 100%;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #666;
        }
        .error {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #d32f2f;
            flex-direction: column;
        }
        .error button {
            margin-top: 20px;
            padding: 10px 20px;
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        /* Excalidraw specific styles */
        .excalidraw {
            height: 100%;
            width: 100%;
        }
        .excalidraw .App-menu_top {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">正在加载 Excalidraw...</div>
    </div>

    <!-- Load React and ReactDOM from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

    <!-- Load Socket.IO -->
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>

    <!-- 加载Excalidraw -->
    <script type = "module">
        let socket;
        let groupInfo = null;
        let excalidrawAPI = null;
        // let drawingData = [];

        // 简化的错误处理
        function showError(message) {
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="error">
                    <div>错误: ${message}</div>
                    <button onclick="location.reload()">重新加载</button>
                </div>
            `;
        }

        // 检查必要的依赖
        if (typeof React === 'undefined') {
            showError('React 库加载失败');
        } else if (typeof ReactDOM === 'undefined') {
            showError('ReactDOM 库加载失败');
        } else if (typeof io === 'undefined') {
            showError('Socket.IO 库加载失败');
        } else {
            initializeExcalidraw();
        }

        async function initializeExcalidraw() {
            try {
                // 初始化 Socket.IO 连接
                socket = io();

                socket.on('connect', () => {
                    console.log('已连接到服务器');
                    // 通过 postMessage 通知父窗口
                    window.parent.postMessage({
                        type: 'EXCALIDRAW_READY',
                        payload: { connected: true }
                    }, '*');
                });

                socket.on('whiteboard_update', (data) => {
                    if (excalidrawAPI && data.elements) {
                        try {
                            excalidrawAPI.updateScene({
                                elements: data.elements,
                                appState: data.appState || {},
                                commitToHistory: false
                            });
                        } catch (error) {
                            console.error('更新场景失败:', error);
                        }
                    }
                });

                // 监听来自父窗口的消息
                window.addEventListener('message', (event) => {
                    if (event.data.type === 'JOIN_GROUP') {
                        groupInfo = event.data.payload;
                        if (socket && groupInfo) {
                            socket.emit('join_whiteboard', { room: groupInfo.group_id });
                        }
                    }
                });

                // 动态加载 Excalidraw 模块
                let ExcalidrawLib = null;
                let useSimpleDrawing = false;

                try {
                    console.log('开始加载 Excalidraw 模块...');
                    ExcalidrawLib = await import('./assets/index-dxpBGHC9.js');
                    console.log('Excalidraw 模块加载成功:', ExcalidrawLib);
                    console.log('可用的导出:', Object.keys(ExcalidrawLib));
                } catch (error) {
                    console.error('加载 Excalidraw 模块失败:', error);
                }

                // 创建 Excalidraw 应用
                const App = () => {
                    const [isReady, setIsReady] = React.useState(false);
                    const [error, setError] = React.useState(null);
                    const [debugInfo, setDebugInfo] = React.useState('');

                    React.useEffect(() => {
                        // 详细检查 Excalidraw 组件是否可用
                        console.log('检查 ExcalidrawLib:', ExcalidrawLib);

                        if (!ExcalidrawLib) {
                            setError('ExcalidrawLib 未加载');
                            setDebugInfo('模块加载失败');
                            return;
                        }

                        // 检查可用的导出
                        const exports = Object.keys(ExcalidrawLib);
                        console.log('ExcalidrawLib 导出:', exports);
                        setDebugInfo('可用导出: ' + exports.join(', '));

                        // 尝试找到 Excalidraw 组件
                        let foundComponent = false;

                        if (ExcalidrawLib.Excalidraw) {
                            console.log('找到 ExcalidrawLib.Excalidraw');
                            foundComponent = true;
                        } else if (ExcalidrawLib.default && typeof ExcalidrawLib.default === 'function') {
                            console.log('找到 ExcalidrawLib.default (函数)');
                            foundComponent = true;
                        } else if (ExcalidrawLib.default && ExcalidrawLib.default.Excalidraw) {
                            console.log('找到 ExcalidrawLib.default.Excalidraw');
                            foundComponent = true;
                        } else {
                            // 查找任何可能是 React 组件的导出
                            for (const key of exports) {
                                const exportValue = ExcalidrawLib[key];
                                if (typeof exportValue === 'function' ||
                                    (exportValue && typeof exportValue === 'object' && exportValue.$$typeof)) {
                                    console.log('找到可能的组件:', key);
                                    foundComponent = true;
                                    break;
                                }
                            }
                        }

                        if (foundComponent) {
                            setIsReady(true);
                        } else {
                            setError('未找到可用的 Excalidraw 组件');
                        }
                    }, []);

                    const handleChange = React.useCallback((elements, appState, files) => {
                        // 避免在拖拽等操作时频繁发送更新
                        if (appState && (
                            appState.draggingElement ||
                            appState.resizingElement ||
                            appState.editingElement ||
                            appState.writing
                        )) {
                            return;
                        }

                        if (socket && groupInfo) {
                            socket.emit('whiteboard_update', {
                                elements: elements,
                                appState: {
                                    viewBackgroundColor: appState.viewBackgroundColor,
                                    scrollX: appState.scrollX,
                                    scrollY: appState.scrollY,
                                    zoom: appState.zoom
                                }
                            });
                        }

                        // 通知父窗口内容已更改
                        window.parent.postMessage({
                            type: 'EXCALIDRAW_CHANGE',
                            payload: { elements, appState }
                        }, '*');
                    }, []);

                    if (error) {
                        return React.createElement('div', { className: 'error' }, [
                            React.createElement('div', { key: 'error' }, error),
                            React.createElement('div', {
                                key: 'debug',
                                style: { fontSize: '12px', marginTop: '10px', color: '#666' }
                            }, debugInfo),
                            React.createElement('button', {
                                key: 'reload',
                                onClick: () => location.reload()
                            }, '重新加载')
                        ]);
                    }

                    if (!isReady) {
                        return React.createElement('div', { className: 'loading' }, '正在初始化 Excalidraw...');
                    }

                    // 获取 Excalidraw 组件
                    let ExcalidrawComponent = null;

                    // 根据实际的导出结构获取组件
                    // 从控制台看到导出是 ["_", "c", "g", "s"]
                    // 通常 "_" 是主要的导出，让我们尝试使用它

                    console.log('尝试获取 Excalidraw 组件...');

                    if (ExcalidrawLib._) {
                        // 检查 _ 导出是否是一个函数或组件
                        console.log('检查 ExcalidrawLib._:', typeof ExcalidrawLib._);
                        console.log('ExcalidrawLib._ 的属性:', Object.keys(ExcalidrawLib._ || {}));

                        if (typeof ExcalidrawLib._ === 'function') {
                            ExcalidrawComponent = ExcalidrawLib._;
                            console.log('使用 ExcalidrawLib._ 作为组件');
                        } else if (ExcalidrawLib._ && typeof ExcalidrawLib._ === 'object') {
                            // 检查对象的属性
                            const underscoreProps = Object.keys(ExcalidrawLib._);
                            console.log('ExcalidrawLib._ 对象属性:', underscoreProps);

                            if (ExcalidrawLib._.Excalidraw) {
                                ExcalidrawComponent = ExcalidrawLib._.Excalidraw;
                                console.log('使用 ExcalidrawLib._.Excalidraw');
                            } else if (ExcalidrawLib._.default) {
                                ExcalidrawComponent = ExcalidrawLib._.default;
                                console.log('使用 ExcalidrawLib._.default');
                            } else {
                                // 尝试第一个看起来像组件的属性
                                for (const prop of underscoreProps) {
                                    const value = ExcalidrawLib._[prop];
                                    if (typeof value === 'function') {
                                        ExcalidrawComponent = value;
                                        console.log(`使用 ExcalidrawLib._.${prop} 作为组件`);
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    // 如果还没找到，尝试其他导出
                    if (!ExcalidrawComponent) {
                        for (const key of ['c', 'g', 's']) {
                            const exportValue = ExcalidrawLib[key];
                            console.log(`检查 ExcalidrawLib.${key}:`, typeof exportValue);

                            if (typeof exportValue === 'function') {
                                ExcalidrawComponent = exportValue;
                                console.log(`使用 ExcalidrawLib.${key} 作为组件`);
                                break;
                            } else if (exportValue && exportValue.Excalidraw) {
                                ExcalidrawComponent = exportValue.Excalidraw;
                                console.log(`使用 ExcalidrawLib.${key}.Excalidraw`);
                                break;
                            }
                        }
                    }

                    if (!ExcalidrawComponent) {
                        console.error('无法找到 Excalidraw 组件，可用导出:', Object.keys(ExcalidrawLib));
                        return React.createElement('div', { className: 'error' }, [
                            React.createElement('div', { key: 'msg' }, '无法找到 Excalidraw 组件'),
                            React.createElement('div', {
                                key: 'debug',
                                style: { fontSize: '12px', marginTop: '10px', color: '#666' }
                            }, '可用导出: ' + Object.keys(ExcalidrawLib).join(', ')),
                            React.createElement('button', {
                                key: 'reload',
                                onClick: () => location.reload(),
                                style: { marginTop: '10px' }
                            }, '重新加载')
                        ]);
                    }

                    // 创建异步组件包装器
                    const AsyncExcalidrawWrapper = () => {
                        const [excalidrawReady, setExcalidrawReady] = React.useState(false);
                        const [excalidrawError, setExcalidrawError] = React.useState(null);
                        const [actualComponent, setActualComponent] = React.useState(null);

                        React.useEffect(() => {
                            const loadExcalidraw = async () => {
                                try {
                                    console.log('开始异步加载 Excalidraw 组件...');

                                    // 如果组件是一个函数，尝试调用它
                                    if (typeof ExcalidrawComponent === 'function') {
                                        console.log('调用 ExcalidrawComponent 函数...');

                                        // 尝试调用函数，看看返回什么
                                        let result;
                                        try {
                                            result = ExcalidrawComponent();
                                            console.log('函数调用结果:', result);
                                            console.log('结果类型:', typeof result);

                                            // 如果返回Promise，等待解析
                                            if (result && typeof result.then === 'function') {
                                                console.log('等待Promise解析...');
                                                const resolved = await result;
                                                console.log('Promise解析结果:', resolved);
                                                setActualComponent(() => resolved);
                                            } else if (result && (typeof result === 'function' || result.$$typeof)) {
                                                // 如果直接返回组件
                                                console.log('直接使用返回的组件');
                                                setActualComponent(() => result);
                                            } else {
                                                // 如果返回的不是组件，尝试使用原函数
                                                console.log('使用原函数作为组件');
                                                setActualComponent(() => ExcalidrawComponent);
                                            }
                                        } catch (callError) {
                                            console.log('函数调用失败，尝试直接使用:', callError);
                                            setActualComponent(() => ExcalidrawComponent);
                                        }
                                    } else {
                                        console.log('直接使用组件');
                                        setActualComponent(() => ExcalidrawComponent);
                                    }

                                    setExcalidrawReady(true);
                                } catch (error) {
                                    console.error('加载 Excalidraw 失败:', error);
                                    setExcalidrawError(error.message);
                                }
                            };

                            loadExcalidraw();
                        }, []);

                        if (excalidrawError) {
                            return React.createElement('div', { className: 'error' }, [
                                React.createElement('div', { key: 'msg' }, '加载 Excalidraw 失败'),
                                React.createElement('div', {
                                    key: 'error',
                                    style: { fontSize: '12px', marginTop: '10px', color: '#666' }
                                }, excalidrawError),
                                React.createElement('button', {
                                    key: 'reload',
                                    onClick: () => location.reload(),
                                    style: { marginTop: '10px' }
                                }, '重新加载')
                            ]);
                        }

                        if (!excalidrawReady || !actualComponent) {
                            return React.createElement('div', { className: 'loading' }, '正在加载 Excalidraw...');
                        }

                        // 尝试创建实际的组件
                        try {
                            console.log('创建实际的 Excalidraw 组件...');
                            return React.createElement(actualComponent, {
                                ref: (api) => {
                                    excalidrawAPI = api;
                                    console.log('Excalidraw API 已设置:', api);
                                },
                                onChange: handleChange
                            });
                        } catch (renderError) {
                            console.error('渲染 Excalidraw 组件失败:', renderError);
                            return React.createElement('div', { className: 'error' }, [
                                React.createElement('div', { key: 'msg' }, '渲染 Excalidraw 组件失败'),
                                React.createElement('div', {
                                    key: 'error',
                                    style: { fontSize: '12px', marginTop: '10px', color: '#666' }
                                }, renderError.message)
                            ]);
                        }
                    };

                    return React.createElement(AsyncExcalidrawWrapper);
                };

                // 渲染应用
                const root = ReactDOM.createRoot(document.getElementById('root'));
                root.render(React.createElement(App));

            } catch (error) {
                console.error('Excalidraw 初始化错误:', error);
                showError('Excalidraw 初始化失败: ' + error.message);
            }
        }
    </script>
</body>
</html>