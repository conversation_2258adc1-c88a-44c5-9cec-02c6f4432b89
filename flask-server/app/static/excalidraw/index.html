<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <title>Excalidraw</title>
    <!-- Load Excalidraw CSS -->
    <link rel="stylesheet" href="./assets/index-Bnrc8hOs.css">
    <style>
        html, body, #root {
            height: 100%;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #666;
        }
        .error {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #d32f2f;
            flex-direction: column;
        }
        .error button {
            margin-top: 20px;
            padding: 10px 20px;
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .simple-canvas {
            width: 100%;
            height: 100%;
            border: none;
            cursor: crosshair;
        }
        .toolbar {
            padding: 10px;
            background: #f5f5f5;
            border-bottom: 1px solid #ddd;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .toolbar button {
            padding: 5px 10px;
            border: 1px solid #ccc;
            background: white;
            border-radius: 3px;
            cursor: pointer;
        }
        .toolbar button:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">正在加载 Excalidraw...</div>
    </div>

    <!-- Load React and ReactDOM from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

    <!-- Load Socket.IO -->
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>

    <!-- 加载Excalidraw -->
    <script type = "module">
        let socket;
        let groupInfo = null;
        let excalidrawAPI = null;
        // let drawingData = [];

        // 简化的错误处理
        function showError(message) {
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="error">
                    <div>错误: ${message}</div>
                    <button onclick="location.reload()">重新加载</button>
                </div>
            `;
        }

        // 检查必要的依赖
        if (typeof React === 'undefined') {
            showError('React 库加载失败');
        } else if (typeof ReactDOM === 'undefined') {
            showError('ReactDOM 库加载失败');
        } else if (typeof io === 'undefined') {
            showError('Socket.IO 库加载失败');
        } else {
            initializeExcalidraw();
        }

        

       
    </script>
</body>
</html>