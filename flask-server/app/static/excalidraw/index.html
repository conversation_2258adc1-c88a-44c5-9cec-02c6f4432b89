<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <title>Excalidraw</title>
    <!-- Load Excalidraw CSS -->
    <link rel="stylesheet" href="./assets/index-Bnrc8hOs.css">
    <style>
        html, body, #root {
            height: 100%;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #666;
        }
        .error {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #d32f2f;
            flex-direction: column;
        }
        .error button {
            margin-top: 20px;
            padding: 10px 20px;
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        /* Excalidraw specific styles */
        .excalidraw {
            height: 100%;
            width: 100%;
        }
        .excalidraw .App-menu_top {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">正在加载 Excalidraw...</div>
    </div>

    <!-- Load React and ReactDOM from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

    <!-- Load Socket.IO -->
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>

    <!-- 加载Excalidraw -->
    <script type = "module">
        let socket;
        let groupInfo = null;
        let excalidrawAPI = null;
        // let drawingData = [];

        // 简化的错误处理
        function showError(message) {
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="error">
                    <div>错误: ${message}</div>
                    <button onclick="location.reload()">重新加载</button>
                </div>
            `;
        }

        // 检查必要的依赖
        if (typeof React === 'undefined') {
            showError('React 库加载失败');
        } else if (typeof ReactDOM === 'undefined') {
            showError('ReactDOM 库加载失败');
        } else if (typeof io === 'undefined') {
            showError('Socket.IO 库加载失败');
        } else {
            initializeExcalidraw();
        }

        async function initializeExcalidraw() {
            try {
                // 初始化 Socket.IO 连接
                socket = io();

                socket.on('connect', () => {
                    console.log('已连接到服务器');
                    // 通过 postMessage 通知父窗口
                    window.parent.postMessage({
                        type: 'EXCALIDRAW_READY',
                        payload: { connected: true }
                    }, '*');
                });

                socket.on('whiteboard_update', (data) => {
                    if (excalidrawAPI && data.elements) {
                        try {
                            excalidrawAPI.updateScene({
                                elements: data.elements,
                                appState: data.appState || {},
                                commitToHistory: false
                            });
                        } catch (error) {
                            console.error('更新场景失败:', error);
                        }
                    }
                });

                // 监听来自父窗口的消息
                window.addEventListener('message', (event) => {
                    if (event.data.type === 'JOIN_GROUP') {
                        groupInfo = event.data.payload;
                        if (socket && groupInfo) {
                            socket.emit('join_whiteboard', { room: groupInfo.group_id });
                        }
                    }
                });

                // 动态加载 Excalidraw 模块
                let ExcalidrawLib = null;
                let useSimpleDrawing = false;

                try {
                    console.log('开始加载 Excalidraw 模块...');
                    ExcalidrawLib = await import('./assets/index-dxpBGHC9.js');
                    console.log('Excalidraw 模块加载成功:', ExcalidrawLib);
                    console.log('可用的导出:', Object.keys(ExcalidrawLib));
                } catch (error) {
                    console.error('加载 Excalidraw 模块失败:', error);
                }

                // 创建 Excalidraw 应用
                const App = () => {
                    const [isReady, setIsReady] = React.useState(false);
                    const [error, setError] = React.useState(null);
                    const [debugInfo, setDebugInfo] = React.useState('');

                    React.useEffect(() => {
                        // 详细检查 Excalidraw 组件是否可用
                        console.log('检查 ExcalidrawLib:', ExcalidrawLib);

                        if (!ExcalidrawLib) {
                            setError('ExcalidrawLib 未加载');
                            setDebugInfo('模块加载失败');
                            return;
                        }

                        // 检查可用的导出
                        const exports = Object.keys(ExcalidrawLib);
                        console.log('ExcalidrawLib 导出:', exports);
                        setDebugInfo('可用导出: ' + exports.join(', '));

                        // 尝试找到 Excalidraw 组件
                        let foundComponent = false;

                        if (ExcalidrawLib.Excalidraw) {
                            console.log('找到 ExcalidrawLib.Excalidraw');
                            foundComponent = true;
                        } else if (ExcalidrawLib.default && typeof ExcalidrawLib.default === 'function') {
                            console.log('找到 ExcalidrawLib.default (函数)');
                            foundComponent = true;
                        } else if (ExcalidrawLib.default && ExcalidrawLib.default.Excalidraw) {
                            console.log('找到 ExcalidrawLib.default.Excalidraw');
                            foundComponent = true;
                        } else {
                            // 查找任何可能是 React 组件的导出
                            for (const key of exports) {
                                const exportValue = ExcalidrawLib[key];
                                if (typeof exportValue === 'function' ||
                                    (exportValue && typeof exportValue === 'object' && exportValue.$$typeof)) {
                                    console.log('找到可能的组件:', key);
                                    foundComponent = true;
                                    break;
                                }
                            }
                        }

                        if (foundComponent) {
                            setIsReady(true);
                        } else {
                            setError('未找到可用的 Excalidraw 组件');
                        }
                    }, []);

                    const handleChange = React.useCallback((elements, appState, files) => {
                        // 避免在拖拽等操作时频繁发送更新
                        if (appState && (
                            appState.draggingElement ||
                            appState.resizingElement ||
                            appState.editingElement ||
                            appState.writing
                        )) {
                            return;
                        }

                        if (socket && groupInfo) {
                            socket.emit('whiteboard_update', {
                                elements: elements,
                                appState: {
                                    viewBackgroundColor: appState.viewBackgroundColor,
                                    scrollX: appState.scrollX,
                                    scrollY: appState.scrollY,
                                    zoom: appState.zoom
                                }
                            });
                        }

                        // 通知父窗口内容已更改
                        window.parent.postMessage({
                            type: 'EXCALIDRAW_CHANGE',
                            payload: { elements, appState }
                        }, '*');
                    }, []);

                    if (error) {
                        return React.createElement('div', { className: 'error' }, [
                            React.createElement('div', { key: 'error' }, error),
                            React.createElement('div', {
                                key: 'debug',
                                style: { fontSize: '12px', marginTop: '10px', color: '#666' }
                            }, debugInfo),
                            React.createElement('button', {
                                key: 'reload',
                                onClick: () => location.reload()
                            }, '重新加载')
                        ]);
                    }

                    if (!isReady) {
                        return React.createElement('div', { className: 'loading' }, '正在初始化 Excalidraw...');
                    }

                    // 获取 Excalidraw 组件
                    let ExcalidrawComponent = null;

                    // 尝试多种方式获取 Excalidraw 组件
                    if (ExcalidrawLib.Excalidraw) {
                        ExcalidrawComponent = ExcalidrawLib.Excalidraw;
                        console.log('找到 ExcalidrawLib.Excalidraw');
                    } else if (ExcalidrawLib.default && ExcalidrawLib.default.Excalidraw) {
                        ExcalidrawComponent = ExcalidrawLib.default.Excalidraw;
                        console.log('找到 ExcalidrawLib.default.Excalidraw');
                    } else if (ExcalidrawLib.default) {
                        ExcalidrawComponent = ExcalidrawLib.default;
                        console.log('使用 ExcalidrawLib.default');
                    } else {
                        // 尝试查找其他可能的导出
                        const possibleExports = Object.keys(ExcalidrawLib).filter(key =>
                            key.toLowerCase().includes('excalidraw')
                        );
                        console.log('可能的 Excalidraw 导出:', possibleExports);

                        if (possibleExports.length > 0) {
                            ExcalidrawComponent = ExcalidrawLib[possibleExports[0]];
                            console.log('使用:', possibleExports[0]);
                        }
                    }

                    if (!ExcalidrawComponent) {
                        console.error('无法找到 Excalidraw 组件，可用导出:', Object.keys(ExcalidrawLib));
                        return React.createElement('div', { className: 'error' },
                            React.createElement('div', null, '无法找到 Excalidraw 组件'),
                            React.createElement('div', { style: { fontSize: '12px', marginTop: '10px' } },
                                '可用导出: ' + Object.keys(ExcalidrawLib).join(', ')
                            )
                        );
                    }

                    return React.createElement(ExcalidrawComponent, {
                        ref: (api) => {
                            excalidrawAPI = api;
                            console.log('Excalidraw API 已设置:', api);
                        },
                        onChange: handleChange,
                        langCode: "zh-CN",
                        theme: "light",
                        initialData: {
                            elements: [],
                            appState: {
                                viewBackgroundColor: "#ffffff",
                                currentItemStrokeColor: "#000000",
                                currentItemBackgroundColor: "transparent",
                                currentItemFillStyle: "hachure",
                                currentItemStrokeWidth: 1,
                                currentItemStrokeStyle: "solid",
                                currentItemRoughness: 1,
                                currentItemOpacity: 100,
                                currentItemFontFamily: 1,
                                currentItemFontSize: 20,
                                currentItemTextAlign: "left",
                                currentItemStartArrowhead: null,
                                currentItemEndArrowhead: "arrow",
                                gridSize: null,
                                colorPalette: {}
                            }
                        },
                        UIOptions: {
                            canvasActions: {
                                loadScene: false,
                                saveToActiveFile: false,
                                export: {
                                    saveFileToDisk: false
                                },
                                toggleTheme: false
                            },
                            tools: {
                                image: false
                            }
                        },
                        detectScroll: false,
                        handleKeyboardGlobally: true,
                        autoFocus: true,
                        onPointerUpdate: (payload) => {
                            // 可以在这里处理指针更新，用于协同功能
                            if (socket && groupInfo) {
                                socket.emit('pointer_update', payload);
                            }
                        }
                    });
                };

                // 渲染应用
                const root = ReactDOM.createRoot(document.getElementById('root'));
                root.render(React.createElement(App));

            } catch (error) {
                console.error('Excalidraw 初始化错误:', error);
                showError('Excalidraw 初始化失败: ' + error.message);
            }
        }
    </script>
</body>
</html>