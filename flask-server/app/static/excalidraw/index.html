<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <title>Excalidraw</title>
    <style>
        html, body, #root {
            height: 100%;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #666;
        }
        .error {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #d32f2f;
            flex-direction: column;
        }
        .error button {
            margin-top: 20px;
            padding: 10px 20px;
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">正在加载 Excalidraw...</div>
    </div>

    <!-- Load React and ReactDOM from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

    <!-- Load Socket.IO -->
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>

    <!-- Load Excalidraw -->
    <script type="module">
        let ExcalidrawLib;
        let socket;
        let groupInfo = null;
        let excalidrawAPI = null;

        // 简化的错误处理
        function showError(message) {
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="error">
                    <div>错误: ${message}</div>
                    <button onclick="location.reload()">重新加载</button>
                </div>
            `;
        }

        // 检查必要的依赖
        if (typeof React === 'undefined') {
            showError('React 库加载失败');
        } else if (typeof ReactDOM === 'undefined') {
            showError('ReactDOM 库加载失败');
        } else if (typeof io === 'undefined') {
            showError('Socket.IO 库加载失败');
        } else {
            // 动态加载 Excalidraw
            import('./assets/index-dxpBGHC9.js')
                .then(module => {
                    ExcalidrawLib = module;
                    initializeExcalidraw();
                })
                .catch(error => {
                    console.error('Failed to load Excalidraw:', error);
                    showError('Excalidraw 加载失败');
                });
        }

        function initializeExcalidraw() {
            try {
                // 初始化 Socket.IO 连接
                socket = io();

                socket.on('connect', () => {
                    console.log('已连接到服务器');
                    // 通过 postMessage 通知父窗口
                    window.parent.postMessage({
                        type: 'EXCALIDRAW_READY',
                        payload: { connected: true }
                    }, '*');
                });

                socket.on('whiteboard_update', (data) => {
                    if (excalidrawAPI && data.elements) {
                        excalidrawAPI.updateScene({
                            elements: data.elements,
                            appState: data.appState || {},
                            commitToHistory: false
                        });
                    }
                });

                // 监听来自父窗口的消息
                window.addEventListener('message', (event) => {
                    if (event.data.type === 'JOIN_GROUP') {
                        groupInfo = event.data.payload;
                        if (socket && groupInfo) {
                            socket.emit('join_whiteboard', { room: groupInfo.group_id });
                        }
                    }
                });

                // 创建 Excalidraw 应用
                const App = () => {
                    const [isReady, setIsReady] = React.useState(false);

                    React.useEffect(() => {
                        setIsReady(true);
                    }, []);

                    const handleChange = React.useCallback((elements, appState, files) => {
                        // 避免在拖拽等操作时频繁发送更新
                        if (appState.draggingElement ||
                            appState.resizingElement ||
                            appState.editingElement ||
                            appState.writing) {
                            return;
                        }

                        if (socket && groupInfo) {
                            socket.emit('whiteboard_update', {
                                elements: elements,
                                appState: {
                                    viewBackgroundColor: appState.viewBackgroundColor,
                                    scrollX: appState.scrollX,
                                    scrollY: appState.scrollY,
                                    zoom: appState.zoom
                                }
                            });
                        }

                        // 通知父窗口内容已更改
                        window.parent.postMessage({
                            type: 'EXCALIDRAW_CHANGE',
                            payload: { elements, appState }
                        }, '*');
                    }, []);

                    if (!isReady || !ExcalidrawLib?.Excalidraw) {
                        return React.createElement('div', { className: 'loading' }, '正在初始化...');
                    }

                    return React.createElement(ExcalidrawLib.Excalidraw, {
                        ref: (api) => {
                            excalidrawAPI = api;
                        },
                        onChange: handleChange,
                        langCode: "zh-CN",
                        theme: "light",
                        initialData: {
                            elements: [],
                            appState: {
                                viewBackgroundColor: "#ffffff"
                            }
                        }
                    });
                };

                // 渲染应用
                const root = ReactDOM.createRoot(document.getElementById('root'));
                root.render(React.createElement(App));

            } catch (error) {
                console.error('Excalidraw initialization error:', error);
                showError('Excalidraw 初始化失败');
            }
        }
    </script>
</body>
</html>