<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>协同白板</title>
    <style>
        html, body, iframe { margin: 0; padding: 0; height: 100%; width: 100%; border: none; overflow: hidden; }
        .group-controls { position: absolute; top: 1rem; right: 1rem; z-index: 10; background: #fff; padding: 0.5rem 1rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); display: flex; align-items: center; gap: 1rem; }
        .group-controls button { padding: 0.5rem 1rem; border-radius: 5px; border: 1px solid #ccc; cursor: pointer; }
        .group-controls button:hover { background: #f0f0f0; }
    </style>
</head>
<body>
    <iframe id="excalidraw-iframe" src="{{ url_for('static', filename='excalidraw/index.html') }}"></iframe>
    <div class="group-controls">
        <div id="group-info">未加入小组</div>
        <button id="join-group-btn" style="display: none;">加入小组</button>
    </div>

    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <script>
        const iframe = document.getElementById('excalidraw-iframe');
        let groupInfo = null;
        let isExcalidrawReady = false;

        // 等待iframe加载完成
        iframe.onload = () => {
            console.log('Excalidraw iframe 已加载');
        };

        // 获取用户小组信息
        fetch('/student/api/my_group')
            .then(res => res.json())
            .then(data => {
                if (data.success && data.group) {
                    groupInfo = data.group;
                    document.getElementById('group-info').innerText = `小组: ${data.group.group_name}`;
                    document.getElementById('join-group-btn').style.display = 'block';
                } else {
                    document.getElementById('group-info').innerText = '未分配小组';
                }
            })
            .catch(error => {
                console.error('获取小组信息失败:', error);
                document.getElementById('group-info').innerText = '获取小组信息失败';
            });

        // 加入小组按钮点击事件
        document.getElementById('join-group-btn').onclick = () => {
            if (groupInfo && isExcalidrawReady) {
                // 通知iframe加入小组
                iframe.contentWindow.postMessage({
                    type: 'JOIN_GROUP',
                    payload: groupInfo
                }, '*');

                document.getElementById('join-group-btn').style.display = 'none';
                document.getElementById('group-info').innerText = `已连接: ${groupInfo.group_name}`;

                console.log(`已加入小组: ${groupInfo.group_name}`);
            } else {
                if (!groupInfo) {
                    alert('未获取到小组信息');
                } else if (!isExcalidrawReady) {
                    alert('Excalidraw 还未准备就绪，请稍后再试');
                }
            }
        };

        // 监听来自iframe的消息
        window.addEventListener('message', (event) => {
            // 基本安全检查
            if (event.source !== iframe.contentWindow) return;

            const { type, payload } = event.data;

            switch (type) {
                case 'EXCALIDRAW_READY':
                    console.log('Excalidraw 已准备就绪');
                    isExcalidrawReady = true;
                    break;

                case 'EXCALIDRAW_CHANGE':
                    // 这里可以添加额外的处理逻辑，比如保存到本地存储
                    console.log('Excalidraw 内容已更改');
                    break;

                default:
                    console.log('收到未知消息类型:', type);
            }
        });

        // 错误处理
        window.addEventListener('error', (event) => {
            console.error('页面错误:', event.error);
        });

        iframe.onerror = () => {
            console.error('Excalidraw iframe 加载失败');
            document.getElementById('group-info').innerText = 'Excalidraw 加载失败';
        };
    </script>
</body>
</html>