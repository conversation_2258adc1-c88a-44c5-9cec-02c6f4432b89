name: "Bundle Size check @excalidraw/excalidraw"
on:
  pull_request:
    branches:
      - master
jobs:
  size:
    runs-on: ubuntu-latest
    env:
      CI_JOB_NUMBER: 1
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Setup Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
      - name: Install in packages/excalidraw
        run: yarn
        working-directory: packages/excalidraw
        env:
          CI: true
      - uses: andresz1/size-limit-action@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          build_script: build:esm
          skip_step: install
          directory: packages/excalidraw
