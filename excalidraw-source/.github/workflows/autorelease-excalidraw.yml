name: Auto release excalidraw next
on:
  push:
    branches:
      - release

jobs:
  Auto-release-excalidraw-next:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 2
      - name: Setup Node.js 18.x
        uses: actions/setup-node@v2
        with:
          node-version: 18.x
      - name: Set up publish access
        run: |
          npm config set //registry.npmjs.org/:_authToken ${NPM_TOKEN}
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Auto release
        run: |
          yarn add @actions/core -W
          yarn release --tag=next --non-interactive
