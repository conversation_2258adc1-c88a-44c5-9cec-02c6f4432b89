---
slug: /@excalidraw/excalidraw/api
---

# API

Currently the **API** is divided into 3 broad categories 👇

- [Props](/docs/@excalidraw/excalidraw/api/props) - The `props` you can pass to the `Excalidraw` component.
- [Children components](/docs/@excalidraw/excalidraw/api/children-components) - Official components you can use to customize the UI.
- [Utils](/docs/@excalidraw/excalidraw/api/utils) - Utilities and helpers you can use to export, restore and more.
