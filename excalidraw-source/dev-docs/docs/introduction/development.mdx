# Development

## Code Sandbox

- Go to https://codesandbox.io/p/github/excalidraw/excalidraw
  - You may need to sign in with GitHub and reload the page
- You can start coding instantly, and even send PRs from there!

## Local Installation

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes.

### Requirements

- [Node.js](https://nodejs.org/en/)
- [Yarn](https://yarnpkg.com/getting-started/install) (v1 or v2.4.2+)
- [Git](https://git-scm.com/downloads)

### Clone the repo

```bash
git clone https://github.com/excalidraw/excalidraw.git
```

### Install the dependencies

```bash
yarn
```

### Start the server

```bash
yarn start
```

Now you can open [http://localhost:3000](http://localhost:3000) and start coding in your favorite code editor.

## Collaboration

For collaboration, you will need to set up [collab server](https://github.com/excalidraw/excalidraw-room) in local.

## Commands

### Install the dependencies

```bash
yarn
```

### Run the project

```bash
yarn start
```

### Reformat all files with Prettier

```bash
yarn fix
```

### Run tests

```bash
yarn test
```

### Update test snapshots

```bash
yarn test:update
```

### Test for formatting with Prettier

```bash
yarn test:code
```

### Docker Compose

You can use docker-compose to work on Excalidraw locally if you don't want to setup a Node.js env.

```bash
docker-compose up --build -d
```

## Self-hosting

We publish a Docker image with the Excalidraw client at [excalidraw/excalidraw](https://hub.docker.com/r/excalidraw/excalidraw). You can use it to self-host your own client under your own domain, on Kubernetes, AWS ECS, etc.

```bash
docker build -t excalidraw/excalidraw .
docker run --rm -dit --name excalidraw -p 5000:80 excalidraw/excalidraw:latest
```

The Docker image is free of analytics and other tracking libraries.

**At the moment, self-hosting your own instance doesn't support sharing or collaboration features.**

We are working towards providing a full-fledged solution for self-hosting your own Excalidraw.
