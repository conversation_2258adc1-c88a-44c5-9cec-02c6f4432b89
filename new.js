 function initializeDrawingApp() {
            try {
                // 初始化 Socket.IO 连接
                socket = io();

                socket.on('connect', () => {
                    console.log('已连接到服务器');
                    // 通过 postMessage 通知父窗口
                    window.parent.postMessage({
                        type: 'EXCALIDRAW_READY',
                        payload: { connected: true }
                    }, '*');
                });

                socket.on('whiteboard_update', (data) => {
                    if (data && data.drawingData) {
                        drawingData = data.drawingData;
                        redrawCanvas();
                    }
                });

                // 监听来自父窗口的消息
                window.addEventListener('message', (event) => {
                    if (event.data.type === 'JOIN_GROUP') {
                        groupInfo = event.data.payload;
                        if (socket && groupInfo) {
                            socket.emit('join_whiteboard', { room: groupInfo.group_id });
                        }
                    }
                });

                // 创建简单的绘图应用
                const App = () => {
                    const canvasRef = React.useRef(null);
                    const [isDrawing, setIsDrawing] = React.useState(false);
                    const [tool, setTool] = React.useState('pen');

                    React.useEffect(() => {
                        const canvas = canvasRef.current;
                        if (canvas) {
                            canvas.width = canvas.offsetWidth;
                            canvas.height = canvas.offsetHeight;

                            const ctx = canvas.getContext('2d');
                            ctx.strokeStyle = '#000000';
                            ctx.lineWidth = 2;
                            ctx.lineCap = 'round';
                            ctx.lineJoin = 'round';
                        }
                    }, []);

                    const startDrawing = (e) => {
                        if (tool !== 'pen') return;

                        const canvas = canvasRef.current;
                        const rect = canvas.getBoundingClientRect();
                        const x = e.clientX - rect.left;
                        const y = e.clientY - rect.top;

                        setIsDrawing(true);

                        const ctx = canvas.getContext('2d');
                        ctx.beginPath();
                        ctx.moveTo(x, y);

                        // 记录绘图数据
                        const drawAction = {
                            type: 'start',
                            x: x,
                            y: y,
                            timestamp: Date.now()
                        };
                        drawingData.push(drawAction);
                    };

                    const draw = (e) => {
                        if (!isDrawing || tool !== 'pen') return;

                        const canvas = canvasRef.current;
                        const rect = canvas.getBoundingClientRect();
                        const x = e.clientX - rect.left;
                        const y = e.clientY - rect.top;

                        const ctx = canvas.getContext('2d');
                        ctx.lineTo(x, y);
                        ctx.stroke();

                        // 记录绘图数据
                        const drawAction = {
                            type: 'draw',
                            x: x,
                            y: y,
                            timestamp: Date.now()
                        };
                        drawingData.push(drawAction);

                        // 发送更新到服务器
                        if (socket && groupInfo) {
                            socket.emit('whiteboard_update', {
                                drawingData: drawingData.slice(-10) // 只发送最近的10个点
                            });
                        }
                    };

                    const stopDrawing = () => {
                        setIsDrawing(false);

                        // 记录结束绘图
                        const drawAction = {
                            type: 'end',
                            timestamp: Date.now()
                        };
                        drawingData.push(drawAction);

                        // 通知父窗口内容已更改
                        window.parent.postMessage({
                            type: 'EXCALIDRAW_CHANGE',
                            payload: { drawingData }
                        }, '*');
                    };

                    const clearCanvas = () => {
                        const canvas = canvasRef.current;
                        const ctx = canvas.getContext('2d');
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        drawingData = [];

                        if (socket && groupInfo) {
                            socket.emit('whiteboard_update', {
                                drawingData: []
                            });
                        }
                    };

                    const redrawCanvas = () => {
                        const canvas = canvasRef.current;
                        if (!canvas) return;

                        const ctx = canvas.getContext('2d');
                        ctx.clearRect(0, 0, canvas.width, canvas.height);

                        ctx.beginPath();
                        for (let i = 0; i < drawingData.length; i++) {
                            const action = drawingData[i];
                            if (action.type === 'start') {
                                ctx.moveTo(action.x, action.y);
                            } else if (action.type === 'draw') {
                                ctx.lineTo(action.x, action.y);
                                ctx.stroke();
                            }
                        }
                    };

                    // 将redrawCanvas函数暴露到全局
                    window.redrawCanvas = redrawCanvas;

                    return React.createElement('div', {
                        style: { height: '100%', display: 'flex', flexDirection: 'column' }
                    }, [
                        // 工具栏
                        React.createElement('div', {
                            key: 'toolbar',
                            className: 'toolbar'
                        }, [
                            React.createElement('span', { key: 'title' }, '协同白板'),
                            React.createElement('button', {
                                key: 'pen',
                                onClick: () => setTool('pen'),
                                style: {
                                    backgroundColor: tool === 'pen' ? '#e3f2fd' : 'white'
                                }
                            }, '画笔'),
                            React.createElement('button', {
                                key: 'clear',
                                onClick: clearCanvas
                            }, '清除'),
                            React.createElement('span', {
                                key: 'status',
                                style: { marginLeft: 'auto', fontSize: '12px', color: '#666' }
                            }, groupInfo ? `已连接: ${groupInfo.group_name}` : '等待连接...')
                        ]),
                        // 画布
                        React.createElement('canvas', {
                            key: 'canvas',
                            ref: canvasRef,
                            className: 'simple-canvas',
                            style: { flex: 1 },
                            onMouseDown: startDrawing,
                            onMouseMove: draw,
                            onMouseUp: stopDrawing,
                            onMouseLeave: stopDrawing
                        })
                    ]);
                };

                // 渲染应用
                const root = ReactDOM.createRoot(document.getElementById('root'));
                root.render(React.createElement(App));

            } catch (error) {
                console.error('Drawing app initialization error:', error);
                showError('绘图应用初始化失败: ' + error.message);
            }
        }