async function initializeExcalidraw() {
            try{
                // 等待一下确认所有依赖加载完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 初始化 Sockey.IO 连接
                                socket = io();

                socket.on('connect', () => {
                    console.log('已连接到服务器');
                    // 通过 postMessage 通知父窗口
                    window.parent.postMessage({
                        type: 'EXCALIDRAW_READY',
                        payload: { connected: true }
                    }, '*');
                });

                socket.on('whiteboard_update', (data) => {
                    if (data && data.drawingData) {
                        drawingData = data.drawingData;
                        redrawCanvas();
                    }
                });

                // 监听来自父窗口的消息
                window.addEventListener('message', (event) => {
                    if (event.data.type === 'JOIN_GROUP') {
                        groupInfo = event.data.payload;
                        if (socket && groupInfo) {
                            socket.emit('join_whiteboard', { room: groupInfo.group_id });
                        }
                    }
            });

            // 创建绘图界面
            const App = () => { 
                coonst [isReady, setIsReady] = React.useState(false);
                const [error, setError] = React.useState(null);

                React.useEffect(() =>{
                    const loadExcalidraw = async () =>{
                        try {
                            if (window.ExcalidrawLib){
                                setIsReady(true);
                                return;
                            }

                            const module = await import('./assets/index-dxpBGHC9.js');
                            window.ExcalidrawLib = module;
                            setIsReady(true);
                        }catch(err){
                            console.error('加载Excalidraw失败', err);
                            setError('无法加载绘图组件');
                        }
                    };
        
                    loadExcalidraw();
                 const handleChange = React.useCallback((elements, appState, files) => {
                        // 避免在拖拽等操作时频繁发送更新
                        if (appState && (appState.draggingElement ||
                            appState.resizingElement ||
                            appState.editingElement ||
                            appState.writing)) {
                            return;
                        }
                        if (socket && groupInfo){
                            socket.emit('whiteboard_update', {
                                elements: elements,
                                appState: appState ?{
                            viewBackgroundColor: appState.viewBackgroundColor,
                            scrollX: appState.scrollX,
                            scrollY: appState.scrollY,
                            zoom:appState.zoom
                        }:{}
                    });
                    }

                window.parent.postMessage ({
                    type: 'EXCALIDRAW_CHANGE',
                    payload:{
                        elements,
                        appState
                    }
                }, '*');
            },[]);

                    if (error) {
                        return React.createElement('div', { className: 'error' },
                            React.createElement('div', null, error),
                            React.createElement('button', {
                                onClick: () => location.reload()
                            }, '重新加载')
                        );
                    }

                    if (!isReady) {
                        return React.createElement('div', { className: 'loading' }, '正在加载绘图组件...');
                    }

                    // 尝试创建Excalidraw组件
                    try {
                        const ExcalidrawComponent = window.ExcalidrawLib?.Excalidraw ||
                                                  window.ExcalidrawLib?.default?.Excalidraw ||
                                                  window.ExcalidrawLib?.default;

                        if (!ExcalidrawComponent) {
                            return React.createElement('div', { className: 'error' },
                                '找不到Excalidraw组件'
                            );
                        }

                        return React.createElement(ExcalidrawComponent, {
                            ref: (api) => {
                                excalidrawAPI = api;
                            },
                            onChange: handleChange,
                            langCode: "zh-CN",
                            theme: "light",
                            initialData: {
                                elements: [],
                                appState: {
                                    viewBackgroundColor: "#ffffff"
                                }
                            }
                        });
                    } catch (err) {
                        console.error('创建Excalidraw组件失败:', err);
                        return React.createElement('div', { className: 'error' },
                            '创建绘图组件失败'
                ]);
                    };

                // 渲染应用
                const root = ReactDOM.createRoot(document.getElementById('root'));
                root.render(React.createElement(App));

                }catch(error){
                    console.error('Excalidraw initialization error:', error);
                    showError('Excalidraw 初始化失败: ' + error.message);
                    }
                }